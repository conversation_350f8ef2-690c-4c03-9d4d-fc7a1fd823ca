"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Plus, Edit, Trash2, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { dataClient } from "@/lib/data-client"
import type { Culture } from "@/lib/types"
import { CULTURE_STATUSES, type CultureStatus } from "@/lib/constants"
import { useToast } from "@/hooks/use-toast"
import { db } from "@/lib/db"

interface CulturesSectionProps {
  patientId: string
}

export function CulturesSection({ patientId }: CulturesSectionProps) {
  const [cultures, setCultures] = useState<Culture[]>([])
  const cultureStatuses = CULTURE_STATUSES
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [editingCulture, setEditingCulture] = useState<Culture | null>(null)
  const [viewingCulture, setViewingCulture] = useState<Culture | null>(null)
  const [formData, setFormData] = useState({
    requested_date: new Date().toISOString().split("T")[0],
    result_date: "",
    culture_type: "",
    results: "",
    microorganism: "Awatin Results",
    status: 'awaiting results' as CultureStatus, // Default to 'awaiting results'
  })
  const { toast } = useToast()

  useEffect(() => {
    loadCultures()
  }, [patientId])

  const loadCultures = async () => {
    try {
      const culturesData = await dataClient.getCultures(patientId)
      setCultures(culturesData)
    } catch (error) {
      console.error("Error loading cultures:", error)
    }
  }

  const resetForm = () => {
    setFormData({
      requested_date: new Date().toISOString().split("T")[0],
      result_date: "",
      culture_type: "",
      results: "",
      microorganism: "Awatin Results",
      status: 'awaiting results' as CultureStatus, // Default to 'awaiting results'
    })
    setEditingCulture(null)
  }

  const handleEdit = (culture: Culture) => {
    setEditingCulture(culture)
    setFormData({
      requested_date: culture.requested_date,
      result_date: culture.result_date || "",
      culture_type: culture.culture_type || "",
      results: culture.results || "",
      microorganism: culture.microorganism || "Awatin Results",
      status: culture.status,
    })
    setIsDialogOpen(true)
  }

  const handleView = (culture: Culture) => {
    setViewingCulture(culture)
    setIsViewDialogOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const cultureData = {
        patient_id: patientId,
        requested_date: formData.requested_date,
        result_date: formData.result_date || undefined,
        culture_type: formData.culture_type,
        results: formData.results,
        microorganism: formData.microorganism,
        status: formData.status,
      }

      if (editingCulture) {
        await dataClient.updateCulture(editingCulture.id, cultureData)
      } else {
        await dataClient.insertCulture(cultureData)
      }

      await loadCultures()
      setIsDialogOpen(false)
      resetForm()

      toast({
        title: "Culture saved",
        description: "Culture information has been successfully saved.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save culture information.",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (culture: Culture) => {
    try {
      await dataClient.deleteCulture(culture.id)
      await loadCultures()

      toast({
        title: "Culture deleted",
        description: "Culture has been removed.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete culture.",
        variant: "destructive",
      })
    }
  }

  const getStatusBadgeVariant = (statusName: string) => {
    switch (statusName) {
      case "completed":
        return "secondary"
      case "awaiting results":
      default:
        return "outline"
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Cultures</CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>{editingCulture ? "Edit Culture" : "Add Culture"}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="requested_date">Requested Date</Label>
                  <Input
                    id="requested_date"
                    type="date"
                    value={formData.requested_date}
                    onChange={(e) => setFormData({ ...formData, requested_date: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="result_date">Result Date</Label>
                  <Input
                    id="result_date"
                    type="date"
                    placeholder="dd/mm/yyyy"
                    value={formData.result_date}
                    onChange={(e) => setFormData({ ...formData, result_date: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="culture_type">Culture Type / Specimen Source</Label>
                <Input
                  id="culture_type"
                  placeholder="e.g., Blood, Urine, Sputum"
                  value={formData.culture_type}
                  onChange={(e) => setFormData({ ...formData, culture_type: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="microorganism">Microorganism</Label>
                <Select
                  value={formData.microorganism}
                  onValueChange={(value) => setFormData({ ...formData, microorganism: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Awatin Results">Awatin Results</SelectItem>
                    <SelectItem value="Staph (MSSA)">Staph (MSSA)</SelectItem>
                    <SelectItem value="MRSA">MRSA</SelectItem>
                    <SelectItem value="Streptococcus">Streptococcus</SelectItem>
                    <SelectItem value="Enterococcus">Enterococcus</SelectItem>
                    <SelectItem value="CONS">CONS</SelectItem>
                    <SelectItem value="Klebsiella">Klebsiella</SelectItem>
                    <SelectItem value="E-Coli">E-Coli</SelectItem>
                    <SelectItem value="Pseudomonas">Pseudomonas</SelectItem>
                    <SelectItem value="Proteus">Proteus</SelectItem>
                    <SelectItem value="Acinetobacter">Acinetobacter</SelectItem>
                    <SelectItem value="Enterobacter">Enterobacter</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="results">Culture Results</Label>
                <Textarea
                  id="results"
                  placeholder="Enter detailed results"
                  value={formData.results}
                  onChange={(e) => setFormData({ ...formData, results: e.target.value })}
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) =>
                    setFormData({ ...formData, status: value as CultureStatus })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {cultureStatuses.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status === 'awaiting results' ? 'Awaiting Results' : 
                         status === 'completed' ? 'Completed' : status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">{editingCulture ? "Update Culture" : "Add Culture"}</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent className="p-0">
        {cultures.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">No cultures recorded yet.</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Requested Date</TableHead>
                <TableHead>Culture Type</TableHead>
                <TableHead>Microorganism</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Result Date</TableHead>
                <TableHead>Results</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {cultures.map((culture) => (
                <TableRow key={culture.id}>
                  <TableCell>{new Date(culture.requested_date).toLocaleDateString()}</TableCell>
                  <TableCell>{culture.culture_type || "-"}</TableCell>
                  <TableCell>{culture.microorganism || "-"}</TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(culture.status)}>
                      {culture.status === "awaiting results" ? "Awaiting Results" : 
                       culture.status === "completed" ? "Completed" : culture.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {culture.result_date ? new Date(culture.result_date).toLocaleDateString() : "-"}
                  </TableCell>
                  <TableCell className="max-w-xs truncate">{culture.results || "-"}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm" onClick={() => handleView(culture)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleEdit(culture)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleDelete(culture)}>
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {/* View Culture Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Culture Details</DialogTitle>
          </DialogHeader>
          {viewingCulture && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Requested Date</Label>
                  <p className="text-sm">{new Date(viewingCulture.requested_date).toLocaleDateString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Result Date</Label>
                  <p className="text-sm">
                    {viewingCulture.result_date ? new Date(viewingCulture.result_date).toLocaleDateString() : "-"}
                  </p>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Culture Type / Specimen Source</Label>
                <p className="text-sm">{viewingCulture.culture_type || "-"}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Microorganism</Label>
                <p className="text-sm">{viewingCulture.microorganism || "-"}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                <Badge variant={getStatusBadgeVariant(viewingCulture.status)}>
                  {viewingCulture.status === "awaiting results" ? "Awaiting Results" : 
                   viewingCulture.status === "completed" ? "Completed" : viewingCulture.status}
                </Badge>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Culture Results</Label>
                <p className="text-sm whitespace-pre-wrap">{viewingCulture.results || "-"}</p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  )
}
