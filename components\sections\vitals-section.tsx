"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Plus, Edit } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogHeader, <PERSON>alogTitle, <PERSON>alogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { dataClient } from "@/lib/data-client"
import type { VitalSigns } from "@/lib/types"
import { useToast } from "@/hooks/use-toast"
import { db } from "@/lib/db"

interface VitalsSectionProps {
  patientId: string
}

export function VitalsSection({ patientId }: VitalsSectionProps) {
  const [vitals, setVitals] = useState<VitalSigns[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingVital, setEditingVital] = useState<VitalSigns | null>(null)
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split("T")[0],
    heart_rate: "",
    systolic_bp: "",
    diastolic_bp: "",
    temperature: "",
    respiratory_rate: "",
    oxygen_saturation: "",
    cvp: "",
    fluid_balance: "",
    fluid_intake: "",
    fluid_output: "",
    sofa_score: "",
    apache_score: "",
    rifle_score: "",
    on_ventilator: false,
    on_support: false,
    on_dialysis: false,
  })
  const { toast } = useToast()

  useEffect(() => {
    loadVitals()
  }, [patientId])

  const loadVitals = async () => {
    try {
      const vitalsData = await db.vital_signs.where("patient_id").equals(patientId).toArray()

      // Sort by date in descending order (most recent first)
      vitalsData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

      setVitals(vitalsData)
    } catch (error) {
      console.error("Error loading vitals:", error)
    }
  }

  const resetForm = () => {
    setFormData({
      date: new Date().toISOString().split("T")[0],
      heart_rate: "",
      systolic_bp: "",
      diastolic_bp: "",
      temperature: "",
      respiratory_rate: "",
      oxygen_saturation: "",
      cvp: "",
      fluid_balance: "",
      fluid_intake: "",
      fluid_output: "",
      sofa_score: "",
      apache_score: "",
      rifle_score: "",
      on_ventilator: false,
      on_support: false,
      on_dialysis: false,
    })
    setEditingVital(null)
  }

  const handleEdit = (vital: VitalSigns) => {
    setEditingVital(vital)
    setFormData({
      date: vital.date,
      heart_rate: vital.vital_signs_data?.heart_rate?.toString() || "",
      systolic_bp: vital.vital_signs_data?.systolic_bp?.toString() || "",
      diastolic_bp: vital.vital_signs_data?.diastolic_bp?.toString() || "",
      temperature: vital.vital_signs_data?.temperature?.toString() || "",
      respiratory_rate: vital.vital_signs_data?.respiratory_rate?.toString() || "",
      oxygen_saturation: vital.vital_signs_data?.oxygen_saturation?.toString() || "",
      cvp: vital.vital_signs_data?.cvp?.toString() || "",
      fluid_balance: vital.vital_signs_data?.fluid_balance?.toString() || "",
      fluid_intake: vital.vital_signs_data?.fluid_intake?.toString() || "",
      fluid_output: vital.vital_signs_data?.fluid_output?.toString() || "",
      sofa_score: vital.vital_signs_data?.sofa_score?.toString() || "",
      apache_score: vital.vital_signs_data?.apache_score?.toString() || "",
      rifle_score: vital.vital_signs_data?.rifle_score?.toString() || "",
      on_ventilator: vital.vital_signs_data?.on_ventilator || false,
      on_support: vital.vital_signs_data?.on_support || false,
      on_dialysis: vital.vital_signs_data?.on_dialysis || false,
    })
    setIsDialogOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const vitalData = {
        patient_id: patientId,
        date: formData.date,
        vital_signs_data: {
          heart_rate: formData.heart_rate ? Number.parseInt(formData.heart_rate) : undefined,
          systolic_bp: formData.systolic_bp ? Number.parseInt(formData.systolic_bp) : undefined,
          diastolic_bp: formData.diastolic_bp ? Number.parseInt(formData.diastolic_bp) : undefined,
          temperature: formData.temperature ? Number.parseFloat(formData.temperature) : undefined,
          respiratory_rate: formData.respiratory_rate ? Number.parseInt(formData.respiratory_rate) : undefined,
          oxygen_saturation: formData.oxygen_saturation ? Number.parseInt(formData.oxygen_saturation) : undefined,
          cvp: formData.cvp ? Number.parseInt(formData.cvp) : undefined,
          fluid_balance: formData.fluid_balance ? Number.parseInt(formData.fluid_balance) : undefined,
          fluid_intake: formData.fluid_intake ? Number.parseInt(formData.fluid_intake) : undefined,
          fluid_output: formData.fluid_output ? Number.parseInt(formData.fluid_output) : undefined,
          sofa_score: formData.sofa_score ? Number.parseInt(formData.sofa_score) : undefined,
          apache_score: formData.apache_score ? Number.parseInt(formData.apache_score) : undefined,
          rifle_score: formData.rifle_score ? Number.parseInt(formData.rifle_score) : undefined,
          on_ventilator: formData.on_ventilator,
          on_support: formData.on_support,
          on_dialysis: formData.on_dialysis,
        }
      }

      await dataClient.upsertVitalSigns(vitalData)
      await loadVitals()
      setIsDialogOpen(false)
      resetForm()

      toast({
        title: "Vital signs saved",
        description: "Vital signs have been successfully recorded.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save vital signs.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Plus className="h-5 w-5" />
          Vital Signs
        </CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{editingVital ? "Edit Vital Signs" : "Add Vital Signs"}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                  required
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="systolic_bp">Systolic BP</Label>
                  <Input
                    id="systolic_bp"
                    type="number"
                    placeholder="120"
                    value={formData.systolic_bp}
                    onChange={(e) => setFormData({ ...formData, systolic_bp: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="diastolic_bp">Diastolic BP</Label>
                  <Input
                    id="diastolic_bp"
                    type="number"
                    placeholder="80"
                    value={formData.diastolic_bp}
                    onChange={(e) => setFormData({ ...formData, diastolic_bp: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="heart_rate">HR</Label>
                  <Input
                    id="heart_rate"
                    type="number"
                    placeholder="60-100"
                    value={formData.heart_rate}
                    onChange={(e) => setFormData({ ...formData, heart_rate: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="respiratory_rate">RR</Label>
                  <Input
                    id="respiratory_rate"
                    type="number"
                    placeholder="12-20"
                    value={formData.respiratory_rate}
                    onChange={(e) => setFormData({ ...formData, respiratory_rate: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="oxygen_saturation">O2 Sat</Label>
                  <Input
                    id="oxygen_saturation"
                    type="number"
                    placeholder="95-100"
                    value={formData.oxygen_saturation}
                    onChange={(e) => setFormData({ ...formData, oxygen_saturation: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="temperature">Temp</Label>
                  <Input
                    id="temperature"
                    type="number"
                    step="0.1"
                    placeholder="36.5"
                    value={formData.temperature}
                    onChange={(e) => setFormData({ ...formData, temperature: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cvp">CVP</Label>
                  <Input
                    id="cvp"
                    type="number"
                    placeholder="8"
                    value={formData.cvp}
                    onChange={(e) => setFormData({ ...formData, cvp: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fluid_intake">Intake</Label>
                  <Input
                    id="fluid_intake"
                    type="number"
                    placeholder="0"
                    value={formData.fluid_intake}
                    onChange={(e) => setFormData({ ...formData, fluid_intake: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fluid_output">Output</Label>
                  <Input
                    id="fluid_output"
                    type="number"
                    placeholder="0"
                    value={formData.fluid_output}
                    onChange={(e) => setFormData({ ...formData, fluid_output: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="fluid_balance">Balance</Label>
                <Input
                  id="fluid_balance"
                  type="number"
                  placeholder="0"
                  value={formData.fluid_balance}
                  onChange={(e) => setFormData({ ...formData, fluid_balance: e.target.value })}
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sofa_score">SOFA</Label>
                  <Input
                    id="sofa_score"
                    type="number"
                    placeholder="0"
                    value={formData.sofa_score}
                    onChange={(e) => setFormData({ ...formData, sofa_score: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="apache_score">APACHE</Label>
                  <Input
                    id="apache_score"
                    type="number"
                    placeholder="0"
                    value={formData.apache_score}
                    onChange={(e) => setFormData({ ...formData, apache_score: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="rifle_score">RIFLE</Label>
                  <Input
                    id="rifle_score"
                    type="number"
                    placeholder="0"
                    value={formData.rifle_score}
                    onChange={(e) => setFormData({ ...formData, rifle_score: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="on_ventilator"
                    checked={formData.on_ventilator}
                    onCheckedChange={(checked) => setFormData({ ...formData, on_ventilator: checked })}
                  />
                  <Label htmlFor="on_ventilator">On Ventilator</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="on_support"
                    checked={formData.on_support}
                    onCheckedChange={(checked) => setFormData({ ...formData, on_support: checked })}
                  />
                  <Label htmlFor="on_support">On Support</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="on_dialysis"
                    checked={formData.on_dialysis}
                    onCheckedChange={(checked) => setFormData({ ...formData, on_dialysis: checked })}
                  />
                  <Label htmlFor="on_dialysis">On Dialysis</Label>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">{editingVital ? "Update Vitals" : "Save Vitals"}</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent className="p-0">
        {vitals.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">No vital signs recorded yet.</div>
        ) : (
          <div className="relative">
            {/* Table container with horizontal scroll */}
            <div className="overflow-x-auto">
              <div className="min-w-fit">
                {/* Header row */}
                <div className="flex border-b bg-background sticky top-0 z-20">
                  {/* Sticky first column header */}
                  <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                    <div className="h-20 px-4 flex items-center font-semibold">
                      Vital Sign
                    </div>
                  </div>

                  {/* Date column headers - fixed width */}
                  <div className="flex">
                    {vitals.map((vital) => (
                      <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                        <div className="h-20 px-2 flex flex-col items-center justify-center space-y-2">
                          <div className="font-semibold text-sm">{formatDate(vital.date)}</div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(vital)}
                            className="h-6 text-xs px-2 w-full"
                          >
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                {/* Table body */}
                <div className="divide-y divide-border">
                  {/* BP Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        BP
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <span className="text-sm">
                              {vital.vital_signs_data?.systolic_bp && vital.vital_signs_data?.diastolic_bp
                                ? `${vital.vital_signs_data.systolic_bp}/${vital.vital_signs_data.diastolic_bp}`
                                : "-"}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* HR Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        HR
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <span className="text-sm">
                              {vital.vital_signs_data?.heart_rate || "-"}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* RR Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        RR
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <span className="text-sm">
                              {vital.vital_signs_data?.respiratory_rate || "-"}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* O2Sat Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        O2Sat
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <span className="text-sm">
                              {vital.vital_signs_data?.oxygen_saturation || "-"}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Temp Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        Temp
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <span className="text-sm">
                              {vital.vital_signs_data?.temperature || "-"}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* CVP Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        CVP
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <span className="text-sm">
                              {vital.vital_signs_data?.cvp || "-"}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Balance Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        Balance
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <span className="text-sm">
                              {vital.vital_signs_data?.fluid_balance || "-"}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Intake Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        Intake
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <span className="text-sm">
                              {vital.vital_signs_data?.fluid_intake || "-"}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Output Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        Output
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <span className="text-sm">
                              {vital.vital_signs_data?.fluid_output || "-"}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  {/* SOFA Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        SOFA
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <span className="text-sm">
                              {vital.vital_signs_data?.sofa_score || "-"}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* APACHE Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        APACHE
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <span className="text-sm">
                              {vital.vital_signs_data?.apache_score || "-"}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* RIFLE Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        RIFLE
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <span className="text-sm">
                              {vital.vital_signs_data?.rifle_score || "-"}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* On Ventilator Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        On Ventilator
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <Badge variant={vital.vital_signs_data?.on_ventilator ? "destructive" : "secondary"}>
                              {vital.vital_signs_data?.on_ventilator ? "Yes" : "No"}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* On Support Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        On Support
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <Badge variant={vital.vital_signs_data?.on_support ? "destructive" : "secondary"}>
                              {vital.vital_signs_data?.on_support ? "Yes" : "No"}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* On Dialysis Row */}
                  <div className="flex hover:bg-muted/50 transition-colors">
                    <div className="sticky left-0 z-30 w-36 flex-shrink-0 bg-[#f1f5f9] border-r border-border">
                      <div className="px-2 py-2 flex items-center min-h-[40px] font-medium">
                        On Dialysis
                      </div>
                    </div>
                    <div className="flex">
                      {vitals.map((vital) => (
                        <div key={vital.id} className="w-32 flex-shrink-0 border-r border-border last:border-r-0">
                          <div className="px-2 py-2 text-center flex items-center justify-center min-h-[40px]">
                            <Badge variant={vital.vital_signs_data?.on_dialysis ? "destructive" : "secondary"}>
                              {vital.vital_signs_data?.on_dialysis ? "Yes" : "No"}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
