"use client"

import React, { useState, useEffect } from "react"
import { Plus, ChevronDown, ChevronRight, Edit } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, <PERSON>alogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { dataClient } from "@/lib/data-client"
import type { LabValues } from "@/lib/types"
import { useToast } from "@/hooks/use-toast"
import { db } from "@/lib/db"

interface LabsSectionProps {
  patientId: string
}

// Define the test info type
interface TestInfo {
  name: string
  unit: string
  normal: string
}

const LAB_CATEGORIES = {
  Electrolytes: {
    na: { name: "Na", unit: "mmol/L", normal: "135-145" },
    k: { name: "K", unit: "mmol/L", normal: "3.5-5.0" },
    mg: { name: "Mg", unit: "mg/dL", normal: "1.7-2.2" },
    phosphate: { name: "Phosphate", unit: "mg/dL", normal: "2.5-4.5" },
    ca_ion: { name: "Ca.Ion", unit: "mmol/L", normal: "1.12-1.23" },
    ca_total: { name: "Ca.Total", unit: "mg/dL", normal: "8.5-10.5" },
  },
  Hematology: {
    hgb: { name: "Hgb", unit: "g/dL", normal: "12.0-16.5" },
    plt: { name: "PLT", unit: "×10⁹/L", normal: "150-450" },
    tlc: { name: "TLC", unit: "×10⁹/L", normal: "4.0-11.0" },
    pt: { name: "PT", unit: "sec", normal: "10-13" },
    inr: { name: "INR", unit: "", normal: "0.9-1.2" },
    ptt: { name: "PTT", unit: "sec", normal: "25-35" },
  },
  "Kidney Functions": {
    serum_cr: { name: "Serum.Cr", unit: "mg/dL", normal: "0.6-1.2" },
    serum_urea: { name: "Serum Urea", unit: "mmol/L", normal: "2.5-7.5" },
    bun: { name: "BUN", unit: "mg/dL", normal: "7-20" },
    uric_acid: { name: "Uric Acid", unit: "mg/dL", normal: "M 3.4-7.0; F 2.4-6.0" },
  },
  ABG: {
    ph: { name: "pH", unit: "", normal: "7.35-7.45" },
    co2: { name: "CO2", unit: "mmHg", normal: "35-45" },
    hco3: { name: "HCO3", unit: "mmol/L", normal: "22-26" },
    lactate: { name: "Lactate", unit: "mmol/L", normal: "0.5-2.2" },
  },
  "Liver Functions": {
    albumin: { name: "Albumin", unit: "g/dL", normal: "3.5-5.0" },
    ast: { name: "AST", unit: "U/L", normal: "10-40" },
    alt: { name: "ALT", unit: "U/L", normal: "10-40" },
    total_bili: { name: "Total Bili", unit: "mg/dL", normal: "0.3-1.2" },
    direct_bili: { name: "Direct Bili", unit: "mg/dL", normal: "0.0-0.3" },
  },
  "Inflammatory Markers": {
    crp: { name: "CRP", unit: "mg/L", normal: "<5" },
    esr: { name: "ESR", unit: "mm/hr", normal: "M ≤20; F ≤30" },
    procalcitonin: { name: "Procalcitonin", unit: "ng/mL", normal: "<0.1" },
  },
  Cardiac: {
    troponin: { name: "Troponin", unit: "ng/mL", normal: "≤0.04" },
    ck_mb: { name: "CK-MB", unit: "ng/mL", normal: "0-4" },
    cpk: { name: "CPK", unit: "U/L", normal: "M 50-204; F 36-160" },
  },
  Thyroid: {
    tsh: { name: "TSH", unit: "mIU/L", normal: "0.4-4.0" },
    free_t3: { name: "Free T3", unit: "pg/mL", normal: "2.3-4.2" },
    free_t4: { name: "Free T4", unit: "ng/dL", normal: "0.8-1.8" },
  },
  Other: {
    hba1c: { name: "HBA1C", unit: "%", normal: "4.0-5.6" },
  },
} as const

// Create a flat lookup map for easier access
const TEST_LOOKUP: Record<string, TestInfo> = {}
Object.entries(LAB_CATEGORIES).forEach(([_, tests]) => {
  Object.entries(tests).forEach(([key, test]) => {
    TEST_LOOKUP[key] = test
  })
})

export function LabsSection({ patientId }: LabsSectionProps) {
  const [labs, setLabs] = useState<LabValues[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split("T")[0],
    lab_data: {} as Record<string, string>,
  })
  const [openCategory, setOpenCategory] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    loadLabs()
  }, [patientId])

  const loadLabs = async () => {
    try {
      const labsData = await db.lab_values.where("patient_id").equals(patientId).toArray()

      // Sort by date in descending order (most recent first)
      labsData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

      setLabs(labsData)
    } catch (error) {
      console.error("Error loading labs:", error)
    }
  }

  const resetForm = () => {
    setFormData({
      date: new Date().toISOString().split("T")[0],
      lab_data: {},
    })
    setOpenCategory(null)
    setIsEditMode(false)
  }

  const toggleCategory = (category: string) => {
    setOpenCategory((prev) => (prev === category ? null : category))
  }

  const handleLabValueChange = (key: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      lab_data: {
        ...prev.lab_data,
        [key]: value,
      },
    }))
  }

  const handleEdit = (lab: LabValues) => {
    setIsEditMode(true)
    setFormData({
      date: lab.date,
      lab_data: { ...lab.lab_data },
    })
    setIsDialogOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      // Filter out empty values
      const filteredLabData = Object.fromEntries(
        Object.entries(formData.lab_data).filter(([_, value]) => value.trim() !== ""),
      )

      // The upsertLabValues function automatically handles both insert and update
      await dataClient.upsertLabValues({
        patient_id: patientId,
        date: formData.date,
        lab_data: filteredLabData,
      })

      await loadLabs()
      setIsDialogOpen(false)
      resetForm()

      toast({
        title: isEditMode ? "Lab results updated" : "Lab results saved",
        description: isEditMode 
          ? "Lab values have been successfully updated." 
          : "Lab values have been successfully recorded.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: isEditMode 
          ? "Failed to update lab results." 
          : "Failed to save lab results.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Lab Values</CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {isEditMode ? `Edit Lab Values - ${formatDate(formData.date)}` : "Add Lab Values"}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-4">
                {Object.entries(LAB_CATEGORIES).map(([category, tests]) => (
                  <Collapsible
                    key={category}
                    open={openCategory === category}
                    onOpenChange={() => toggleCategory(category)}
                  >
                    <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-muted rounded-lg hover:bg-muted/80">
                      <span className="font-medium">{category}</span>
                      {openCategory === category ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </CollapsibleTrigger>
                    <CollapsibleContent className="mt-2 space-y-3">
                      <div className="grid grid-cols-2 gap-4">
                        {Object.entries(tests).map(([key, test]) => (
                          <div key={key} className="space-y-1">
                            <Label htmlFor={key} className="text-sm">
                              {test.name} 
                            </Label>
                            <Input
                              id={key}
                              type="number"
                              step="0.01"
                              placeholder={`${test.normal} ${test.unit}`}
                              value={formData.lab_data[key] || ""}
                              onChange={(e) => handleLabValueChange(key, e.target.value)}
                            />
                          </div>
                        ))}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                ))}
              </div>

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  {isEditMode ? "Update Lab Results" : "Save Lab Results"}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent className="p-0">
        {labs.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">No lab results recorded yet.</div>
        ) : (
          <div className="overflow-x-auto p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  {/* First column - Lab Names */}
                  <TableHead className="w-48 bg-[#f1f5f9] font-semibold">Lab Values</TableHead>
                  
                  {/* Date columns with two-row header */}
                  {labs.map((lab) => (
                    <TableHead key={lab.id} className="text-center min-w-24">
                      <div className="space-y-2">
                        <div className="font-semibold">{formatDate(lab.date)}</div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(lab)}
                          className="w-full"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {/* Group lab values by category */}
                {Object.entries(LAB_CATEGORIES).map(([category, tests]) => (
                  <React.Fragment key={category}>
                    {/* Category header row */}
                    <TableRow>
                      <TableCell className="bg-gray-300 text-sm font-semibold p-2" >
                        {category}
                      </TableCell>
                      <TableCell colSpan={labs.length + 1} className="bg-gray-300 p-2" >
                      </TableCell>
                    </TableRow>
                    
                    {/* Individual test rows */}
                    {Object.entries(tests).map(([key, test]) => (
                      <TableRow key={key}>
                        <TableCell className="bg-[#f1f5f9] p-2">
                          <div >{test.name}</div>
                        </TableCell>
                        
                        {/* Values for each date */}
                        {labs.map((lab) => (
                          <TableCell key={lab.id} className="text-center p-2">
                            {lab.lab_data[key] ? (
                              <span >
                                {lab.lab_data[key]} {test.unit}
                              </span>
                            ) : (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
