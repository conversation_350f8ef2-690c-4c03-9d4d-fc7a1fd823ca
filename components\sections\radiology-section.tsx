"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Plus, Edit, Trash2, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON>alogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { dataClient } from "@/lib/data-client"
import type { Radiology } from "@/lib/types"
import { RADIOLOGY_SCAN_TYPES, RADIOLOGY_STATUSES, type RadiologyScanType, type RadiologyStatus } from "@/lib/constants"
import { useToast } from "@/hooks/use-toast"
import { db } from "@/lib/db"

interface RadiologySectionProps {
  patientId: string
}

export function RadiologySection({ patientId }: RadiologySectionProps) {
  const [radiology, setRadiology] = useState<Radiology[]>([])
  const scanTypes = RADIOLOGY_SCAN_TYPES
  const statuses = RADIOLOGY_STATUSES
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [editingRadiology, setEditingRadiology] = useState<Radiology | null>(null)
  const [viewingRadiology, setViewingRadiology] = useState<Radiology | null>(null)
  const [formData, setFormData] = useState({
    scan_type: 'CT' as RadiologyScanType, // Default to CT
    scan_date: new Date().toISOString().split("T")[0],
    body_part: "",
    findings: "",
    status: 'scheduled' as RadiologyStatus, // Default to scheduled
  })
  const { toast } = useToast()

  useEffect(() => {
    loadRadiology()
  }, [patientId])

  const loadRadiology = async () => {
    try {
      const radiologyData = await dataClient.getRadiology(patientId)
      setRadiology(radiologyData)
    } catch (error) {
      console.error("Error loading radiology:", error)
    }
  }

  const resetForm = () => {
    setFormData({
      scan_type: 'CT' as RadiologyScanType, // Default to CT
      scan_date: new Date().toISOString().split("T")[0],
      body_part: "",
      findings: "",
      status: 'scheduled' as RadiologyStatus, // Default to scheduled
    })
    setEditingRadiology(null)
  }

  const handleEdit = (radiologyItem: Radiology) => {
    setEditingRadiology(radiologyItem)
    setFormData({
      scan_type: radiologyItem.scan_type,
      scan_date: radiologyItem.scan_date,
      body_part: radiologyItem.body_part || "",
      findings: radiologyItem.findings || "",
      status: radiologyItem.status,
    })
    setIsDialogOpen(true)
  }

  const handleView = (radiologyItem: Radiology) => {
    setViewingRadiology(radiologyItem)
    setIsViewDialogOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const radiologyData = {
        patient_id: patientId,
        scan_type: formData.scan_type,
        scan_date: formData.scan_date,
        body_part: formData.body_part,
        findings: formData.findings,
        status: formData.status,
      }

      if (editingRadiology) {
        await dataClient.updateRadiology(editingRadiology.id, radiologyData)
      } else {
        await dataClient.insertRadiology(radiologyData)
      }

      await loadRadiology()
      setIsDialogOpen(false)
      resetForm()

      toast({
        title: "Scan saved",
        description: "Radiology information has been successfully saved.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save radiology information.",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (radiologyItem: Radiology) => {
    try {
      await dataClient.deleteRadiology(radiologyItem.id)
      await loadRadiology()

      toast({
        title: "Scan deleted",
        description: "Radiology record has been removed.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete radiology record.",
        variant: "destructive",
      })
    }
  }

  const getStatusBadgeVariant = (statusName: string) => {
    switch (statusName) {
      case "completed":
        return "default"
      case "cancelled":
        return "destructive"
      default:
        return "secondary"
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Radiology</CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>{editingRadiology ? "Edit Scan" : "Add Scan"}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="scan_type">Scan Type</Label>
                  <Select
                    value={formData.scan_type}
                    onValueChange={(value) =>
                      setFormData({ ...formData, scan_type: value as RadiologyScanType })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {scanTypes.map((scanType) => (
                        <SelectItem key={scanType} value={scanType}>
                          {scanType}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="scan_date">Scan Date</Label>
                  <Input
                    id="scan_date"
                    type="date"
                    value={formData.scan_date}
                    onChange={(e) => setFormData({ ...formData, scan_date: e.target.value })}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="body_part">Body Part / Region</Label>
                <Input
                  id="body_part"
                  placeholder="e.g., Chest, Abdomen, Brain"
                  value={formData.body_part}
                  onChange={(e) => setFormData({ ...formData, body_part: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="findings">Findings / Results</Label>
                <Textarea
                  id="findings"
                  placeholder="Enter findings or results"
                  value={formData.findings}
                  onChange={(e) => setFormData({ ...formData, findings: e.target.value })}
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) =>
                    setFormData({ ...formData, status: value as RadiologyStatus })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {statuses.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">{editingRadiology ? "Update Scan" : "Add Scan"}</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent className="p-0">
        {radiology.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">No radiology records yet.</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Scan Type</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Body Part</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Findings</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {radiology.map((radiologyItem) => (
                <TableRow key={radiologyItem.id}>
                  <TableCell className="font-medium">{radiologyItem.scan_type}</TableCell>
                  <TableCell>{new Date(radiologyItem.scan_date).toLocaleDateString()}</TableCell>
                  <TableCell>{radiologyItem.body_part || "-"}</TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(radiologyItem.status)}>
                      {radiologyItem.status.charAt(0).toUpperCase() + radiologyItem.status.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell className="max-w-xs truncate">{radiologyItem.findings || "-"}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm" onClick={() => handleView(radiologyItem)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleEdit(radiologyItem)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleDelete(radiologyItem)}>
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {/* View Radiology Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Radiology Details</DialogTitle>
          </DialogHeader>
          {viewingRadiology && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Scan Type</Label>
                  <p className="text-sm font-medium">{viewingRadiology.scan_type}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Scan Date</Label>
                  <p className="text-sm">{new Date(viewingRadiology.scan_date).toLocaleDateString()}</p>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Body Part / Region</Label>
                <p className="text-sm">{viewingRadiology.body_part || "-"}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                <Badge variant={getStatusBadgeVariant(viewingRadiology.status)}>
                  {viewingRadiology.status.charAt(0).toUpperCase() + viewingRadiology.status.slice(1)}
                </Badge>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Findings / Results</Label>
                <p className="text-sm whitespace-pre-wrap">{viewingRadiology.findings || "-"}</p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  )
}
