"use client"

import type React from "react"
import { db } from "@/lib/db" // Declare the db variable here
import { useState, useEffect } from "react"
import { Plus, Search, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { dataClient } from "@/lib/data-client"
import type { Medication } from "@/lib/types"
import { useToast } from "@/hooks/use-toast"
import { Checkbox } from "@/components/ui/checkbox"

interface MedicationsSectionProps {
  patientId: string
}

export function MedicationsSection({ patientId }: MedicationsSectionProps) {
  const [medications, setMedications] = useState<Medication[]>([])
  const [medicationNames, setMedicationNames] = useState<string[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [newMedicationName, setNewMedicationName] = useState("")
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split("T")[0],
    medication_name: "",
    dosage: "",
    frequency: "",
  })
  const { toast } = useToast()

  // Date grid: last 7 days including today
  const [dates, setDates] = useState<string[]>([])
  // adherenceMap key: `${medicationId}|${yyyy-mm-dd}` => boolean
  const [adherenceMap, setAdherenceMap] = useState<Record<string, boolean>>({})
  const [savingKeys, setSavingKeys] = useState<Record<string, boolean>>({})

  useEffect(() => {
    loadMedications()
    loadMedicationNames()
    // compute last 7 days
    const today = new Date()
    const arr: string[] = []
    for (let i = 6; i >= 0; i--) {
      const d = new Date(today)
      d.setDate(today.getDate() - i)
      arr.push(d.toISOString().split("T")[0])
    }
    setDates(arr)
  }, [patientId])

  const loadMedications = async () => {
    try {
      const medicationsData = await db.medications.where("patient_id").equals(patientId).toArray()

      // Sort by date prescribed in descending order (most recent first)
      medicationsData.sort((a, b) => new Date(b.date_prescribed).getTime() - new Date(a.date_prescribed).getTime())

      setMedications(medicationsData)
      // After medications, load adherence for patient
      const adherence = await dataClient.getMedicationAdherenceByPatient(patientId)
      const map: Record<string, boolean> = {}
      for (const row of adherence) {
        map[`${row.medication_id}|${row.date}`] = !!row.is_taking_medication
      }
      setAdherenceMap(map)
    } catch (error) {
      console.error("Error loading medications:", error)
    }
  }

  const loadMedicationNames = async () => {
    try {
      const names = await dataClient.getMedicationNames()
      setMedicationNames(names)
    } catch (error) {
      console.error("Error loading medication names:", error)
    }
  }

  const resetForm = () => {
    setFormData({
      date: new Date().toISOString().split("T")[0],
      medication_name: "",
      dosage: "",
      frequency: "",
    })
    setSearchTerm("")
    setNewMedicationName("")
  }

  const filteredMedicationNames = medicationNames.filter((name) =>
    name.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleAddNewMedication = async () => {
    if (!newMedicationName.trim()) return

    try {
      await dataClient.insertMedicationName(newMedicationName.trim())
      await loadMedicationNames()
      setFormData({ ...formData, medication_name: newMedicationName.trim() })
      setNewMedicationName("")
      toast({
        title: "Medication added",
        description: "New medication has been added to the catalog.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add new medication to catalog.",
        variant: "destructive",
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.medication_name) {
      toast({
        title: "Error",
        description: "Please select or add a medication name.",
        variant: "destructive",
      })
      return
    }

    try {
      await dataClient.insertMedication({
        patient_id: patientId,
        medication_name: formData.medication_name,
        dosage: formData.dosage,
        frequency: formData.frequency,
        date_prescribed: formData.date,
        is_active: true,
      })

      await loadMedications()
      setIsDialogOpen(false)
      resetForm()

      toast({
        title: "Medication added",
        description: "Medication has been successfully added.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add medication.",
        variant: "destructive",
      })
    }
  }

  const handleToggleActive = async (medication: Medication) => {
    try {
      await dataClient.updateMedication(medication.id, {
        is_active: !medication.is_active,
      })
      await loadMedications()

      toast({
        title: "Medication updated",
        description: `Medication ${medication.is_active ? "deactivated" : "activated"}.`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update medication status.",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (medication: Medication) => {
    try {
      await dataClient.deleteMedication(medication.id)
      await loadMedications()

      toast({
        title: "Medication deleted",
        description: "Medication has been removed.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete medication.",
        variant: "destructive",
      })
    }
  }

  const formatHeaderDate = (isoDate: string) => {
    const d = new Date(isoDate)
    const dd = String(d.getDate()).padStart(2, "0")
    const mm = String(d.getMonth() + 1).padStart(2, "0")
    return `${dd}/${mm}`
  }

  const handleToggleAdherence = async (medicationId: string, dateIso: string, next: boolean) => {
    const key = `${medicationId}|${dateIso}`
    setSavingKeys((prev) => ({ ...prev, [key]: true }))
    setAdherenceMap((prev) => ({ ...prev, [key]: next }))
    try {
      await dataClient.upsertMedicationAdherence({
        patient_id: patientId,
        medication_id: medicationId,
        date: dateIso,
        is_taking_medication: next,
      })
    } catch (error) {
      // rollback on error
      setAdherenceMap((prev) => ({ ...prev, [key]: !next }))
      toast({
        title: "Error",
        description: "Failed to save medication adherence.",
        variant: "destructive",
      })
    } finally {
      setSavingKeys((prev) => ({ ...prev, [key]: false }))
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Medications</CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>Add Medication</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label>Search existing medication</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search and select medication"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                {searchTerm && filteredMedicationNames.length > 0 && (
                  <div className="border rounded-md max-h-32 overflow-y-auto">
                    {filteredMedicationNames.slice(0, 5).map((name) => (
                      <div
                        key={name}
                        className="p-2 hover:bg-muted cursor-pointer"
                        onClick={() => {
                          setFormData({ ...formData, medication_name: name })
                          setSearchTerm("")
                        }}
                      >
                        {name}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label>Add new medication to catalog</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Enter a new medication name"
                    value={newMedicationName}
                    onChange={(e) => setNewMedicationName(e.target.value)}
                  />
                  <Button type="button" onClick={handleAddNewMedication}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {formData.medication_name && (
                <div className="p-2 bg-muted rounded">
                  Selected: <strong>{formData.medication_name}</strong>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="dosage">Dosage</Label>
                <Input
                  id="dosage"
                  placeholder="e.g., 500mg"
                  value={formData.dosage}
                  onChange={(e) => setFormData({ ...formData, dosage: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="frequency">Frequency</Label>
                <Select onValueChange={(value) => setFormData({ ...formData, frequency: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="every 24 hours">every 24 hours</SelectItem>
                    <SelectItem value="every 12 hours">every 12 hours</SelectItem>
                    <SelectItem value="every 8 hours">every 8 hours</SelectItem>
                    <SelectItem value="every 6 hours">every 6 hours</SelectItem>
                    <SelectItem value="every 4 hours">every 4 hours</SelectItem>
                    <SelectItem value="every 2 hours">every 2 hours</SelectItem>
                    <SelectItem value="As Needed">As Needed</SelectItem>
                  </SelectContent>
                </Select>
              </div>



              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Add Medication</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent className="p-0">
        {medications.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">No medications recorded yet.</div>
        ) : (
          <div className="w-full overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="whitespace-nowrap">Medication</TableHead>
                  {dates.map((d) => (
                    <TableHead key={d} className="text-center whitespace-nowrap">{formatHeaderDate(d)}</TableHead>
                  ))}
                  <TableHead className="whitespace-nowrap">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {medications.map((medication) => (
                  <TableRow key={medication.id}>
                    <TableCell className="font-medium">
                      <div className="flex flex-col">
                        <span>{medication.medication_name}</span>
                        <div className="flex flex-wrap gap-1 mt-1 text-xs text-muted-foreground">
                          {medication.dosage && <Badge variant="secondary">{medication.dosage}</Badge>}
                          {medication.frequency && <Badge variant="secondary">{medication.frequency}</Badge>}
                        </div>
                      </div>
                    </TableCell>
                    {dates.map((d) => {
                      const key = `${medication.id}|${d}`
                      const checked = !!adherenceMap[key]
                      return (
                        <TableCell key={key} className="text-center">
                          <Checkbox
                            checked={checked}
                            disabled={!!savingKeys[key]}
                            onCheckedChange={(v) =>
                              handleToggleAdherence(medication.id, d, v === true)
                            }
                          />
                        </TableCell>
                      )
                    })}
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={medication.is_active ? "default" : "secondary"}
                          className="cursor-pointer"
                          onClick={() => handleToggleActive(medication)}
                        >
                          {medication.is_active ? "Active" : "Inactive"}
                        </Badge>
                        <Button variant="ghost" size="sm" onClick={() => handleDelete(medication)}>
                          <Trash2 className="h-4 w-4 text-red-600" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
