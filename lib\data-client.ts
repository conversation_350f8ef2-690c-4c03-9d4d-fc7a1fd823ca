"use client"

import { db } from "./db"
import { supabase } from "./supabase"
import { v4 as uuidv4 } from "uuid"
import type { Patient, VitalSigns, LabValues, Medication, DoctorNote, Culture, Radiology, MedicationAdherence, UnitType } from "./types"
import { GENDER_TYPES, CULTURE_STATUSES, RA<PERSON><PERSON>OGY_SCAN_TYPES, RA<PERSON><PERSON>OGY_STATUSES, USER_ROLES } from "./constants"

class DataClient {
  // Reference data methods (now return hardcoded constants)
  getUserRoles() {
    return [...USER_ROLES]
  }

  getGenderTypes() {
    return [...GENDER_TYPES]
  }

  async getUnitTypes(isOnline: boolean = false): Promise<UnitType[]> {
    if (isOnline) {
      try {
        // Fetch from online database
        const { data: onlineUnits, error } = await supabase
          .from('unit_types')
          .select('id, name')
          .order('name')

        if (error) {
          console.error('Error fetching units from online DB:', error)
          // Fallback to local DB
          return await db.getUnitTypes()
        }

        if (onlineUnits && onlineUnits.length > 0) {
          // Transform online data to match UnitType interface
          const now = new Date().toISOString()
          const transformedUnits: UnitType[] = onlineUnits.map(unit => ({
            ...unit,
            is_active: true,
            created_at: now
          }))
          
          // Update local database with online data
          await db.unit_types.clear()
          await db.unit_types.bulkAdd(transformedUnits)
          return transformedUnits
        }
      } catch (error) {
        console.error('Error in online unit fetch:', error)
      }
    }
    
    // Return from local database (offline mode or online fetch failed)
    return await db.getUnitTypes()
  }

  getCultureStatuses() {
    return [...CULTURE_STATUSES]
  }

  getRadiologyScanTypes() {
    return [...RADIOLOGY_SCAN_TYPES]
  }

  getRadiologyStatuses() {
    return [...RADIOLOGY_STATUSES]
  }

  // Helper method for unit lookup (still needed)
  async getUnitIdByName(name: string): Promise<number | undefined> {
    const unit = await db.unit_types.where('name').equals(name).first()
    return unit?.id
  }
  private async enqueueOperation(
    tableName: string,
    operation: "insert" | "update" | "delete",
    data: any,
    localId?: string,
  ) {
    await db.outbox.add({
      id: uuidv4(),
      table_name: tableName,
      operation,
      data,
      local_id: localId,
      created_at: new Date().toISOString(),
      retries: 0,
    })
  }

  // Patients
  async insertPatient(patient: Omit<Patient, "id" | "created_at" | "updated_at">) {
    const id = uuidv4()
    const now = new Date().toISOString()
    const newPatient: Patient = {
      ...patient,
      id,
      created_at: now,
      updated_at: now,
    }

    await db.patients.add(newPatient)
    await this.enqueueOperation("patients", "insert", newPatient, id)
    return newPatient
  }

  async updatePatient(id: string, updates: Partial<Patient>) {
    const updatedData = { ...updates, updated_at: new Date().toISOString() }
    await db.patients.update(id, updatedData)
    await this.enqueueOperation("patients", "update", { id, ...updatedData })
    return updatedData
  }

  async deletePatient(id: string) {
    await db.transaction(
      "rw",
      [
        db.patients,
        db.vital_signs,
        db.lab_values,
        db.medications,
        db.medication_adherence,
        db.doctor_notes,
        db.cultures,
        db.radiology,
        db.outbox,
      ],
      async () => {
        const patient = await db.patients.get(id)
        if (!patient) return

        const patientId = patient.patient_id

        await Promise.all([
          db.vital_signs.where("patient_id").equals(patientId).delete(),
          db.lab_values.where("patient_id").equals(patientId).delete(),
          db.medications.where("patient_id").equals(patientId).delete(),
          db.medication_adherence.where("patient_id").equals(patientId).delete(),
          db.doctor_notes.where("patient_id").equals(patientId).delete(),
          db.cultures.where("patient_id").equals(patientId).delete(),
          db.radiology.where("patient_id").equals(patientId).delete(),
        ])

        await db.patients.delete(id)

        // Only enqueue the parent delete; server-side FKs will cascade child deletions
        await this.enqueueOperation("patients", "delete", { id })
      },
    )
  }

  async getPatients(
    status?: "active" | "discharged" | "deceased",
  ): Promise<Patient[]> {
    const patients = await db.patients.toArray()
    
    // Fetch unit reference data for joining (only unit_types still exists as table)
    const unitTypes = await db.getUnitTypes()
    const unitMap = new Map(unitTypes.map(u => [u.id, u]))

    let filteredPatients = patients
    if (status === "active") {
      filteredPatients = patients.filter(
        (p) => (p.is_discharged ?? false) === false && (p.is_deceased ?? false) === false,
      )
    } else if (status === "discharged") {
      filteredPatients = patients.filter((p) => (p.is_discharged ?? false) === true)
    } else if (status === "deceased") {
      filteredPatients = patients.filter((p) => (p.is_deceased ?? false) === true)
    }

    // Add joined unit data (gender is now direct string value)
    const enrichedPatients = filteredPatients.map(patient => ({
      ...patient,
      unit: patient.unit_id ? unitMap.get(patient.unit_id) : undefined
    }))

    return enrichedPatients.sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
    )
  }

  async getPatient(id: string): Promise<Patient | undefined> {
    return await db.patients.get(id)
  }

  async getPatientByPatientId(patientId: string): Promise<Patient | undefined> {
    return await db.patients.where("patient_id").equals(patientId).first()
  }

  // Vital Signs
  async upsertVitalSigns(vitalSigns: Omit<VitalSigns, "id" | "created_at">) {
    // Check if a record exists for this patient and date using the compound index
    const existing = await db.vital_signs
      .where("[patient_id+date]")
      .equals([vitalSigns.patient_id, vitalSigns.date])
      .first()

    if (existing) {
      await db.vital_signs.update(existing.id, vitalSigns)
      await this.enqueueOperation("vital_signs", "update", { id: existing.id, ...vitalSigns })
      return { ...existing, ...vitalSigns }
    } else {
      const id = uuidv4()
      const newVitalSigns: VitalSigns = {
        ...vitalSigns,
        id,
        created_at: new Date().toISOString(),
      }
      await db.vital_signs.add(newVitalSigns)
      await this.enqueueOperation("vital_signs", "insert", newVitalSigns, id)
      return newVitalSigns
    }
  }

  async getVitalSigns(patientId: string): Promise<VitalSigns[]> {
    const vitals = await db.vital_signs.where("patient_id").equals(patientId).toArray()
    return vitals.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  }

  // Lab Values
  async upsertLabValues(labValues: Omit<LabValues, "id" | "created_at">) {
    // Check if a record exists for this patient and date using the compound index
    const existing = await db.lab_values
      .where("[patient_id+date]")
      .equals([labValues.patient_id, labValues.date])
      .first()

    if (existing) {
      await db.lab_values.update(existing.id, labValues)
      await this.enqueueOperation("lab_values", "update", { id: existing.id, ...labValues })
      return { ...existing, ...labValues }
    } else {
      const id = uuidv4()
      const newLabValues: LabValues = {
        ...labValues,
        id,
        created_at: new Date().toISOString(),
      }
      await db.lab_values.add(newLabValues)
      await this.enqueueOperation("lab_values", "insert", newLabValues, id)
      return newLabValues
    }
  }

  async getLabValues(patientId: string): Promise<LabValues[]> {
    const labs = await db.lab_values.where("patient_id").equals(patientId).toArray()
    return labs.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  }

  // Medications
  async insertMedication(medication: Omit<Medication, "id" | "created_at">) {
    const id = uuidv4()
    const newMedication: Medication = {
      ...medication,
      id,
      created_at: new Date().toISOString(),
    }

    await db.medications.add(newMedication)
    await this.enqueueOperation("medications", "insert", newMedication, id)
    return newMedication
  }

  async updateMedication(id: string, updates: Partial<Medication>) {
    await db.medications.update(id, updates)
    await this.enqueueOperation("medications", "update", { id, ...updates })
  }

  async deleteMedication(id: string) {
    await db.medications.delete(id)
    await this.enqueueOperation("medications", "delete", { id })
  }

  async getMedications(patientId: string): Promise<Medication[]> {
    const medications = await db.medications.where("patient_id").equals(patientId).toArray()
    return medications.sort((a, b) => new Date(b.date_prescribed).getTime() - new Date(a.date_prescribed).getTime())
  }

  // Medication adherence
  async upsertMedicationAdherence(record: Omit<MedicationAdherence, "id" | "created_at">) {
    // Compound unique key [patient_id+medication_id+date]
    const existing = await db.medication_adherence
      .where("[patient_id+medication_id+date]")
      .equals([record.patient_id, record.medication_id, record.date])
      .first()

    if (existing) {
      await db.medication_adherence.update(existing.id, record)
      await this.enqueueOperation("medication_adherence", "update", { id: existing.id, ...record })
      return { ...existing, ...record }
    } else {
      const id = uuidv4()
      const newRecord: MedicationAdherence = {
        ...record,
        id,
        created_at: new Date().toISOString(),
      }
      await db.medication_adherence.add(newRecord)
      await this.enqueueOperation("medication_adherence", "insert", newRecord, id)
      return newRecord
    }
  }

  async getMedicationAdherenceByPatient(patientId: string): Promise<MedicationAdherence[]> {
    const rows = await db.medication_adherence.where("patient_id").equals(patientId).toArray()
    return rows.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
  }

  // Medication Names
  async insertMedicationName(name: string) {
    const id = uuidv4()
    const medicationName = {
      id,
      name: name.toLowerCase(),
      created_at: new Date().toISOString(),
    }

    await db.medication_names.add(medicationName)
    await this.enqueueOperation("medication_names", "insert", medicationName, id)
    return medicationName
  }

  async getMedicationNames(): Promise<string[]> {
    const names = await db.medication_names.orderBy("name").toArray()
    return names.map((n) => n.name)
  }

  // Doctor Notes
  async insertDoctorNote(note: Omit<DoctorNote, "id" | "created_at">) {
    const id = uuidv4()
    const newNote: DoctorNote = {
      ...note,
      id,
      created_at: new Date().toISOString(),
    }

    await db.doctor_notes.add(newNote)
    await this.enqueueOperation("doctor_notes", "insert", newNote, id)
    return newNote
  }

  async updateDoctorNote(id: string, updates: Partial<DoctorNote>) {
    await db.doctor_notes.update(id, updates)
    await this.enqueueOperation("doctor_notes", "update", { id, ...updates })
  }

  async deleteDoctorNote(id: string) {
    await db.doctor_notes.delete(id)
    await this.enqueueOperation("doctor_notes", "delete", { id })
  }

  async getDoctorNotes(patientId: string): Promise<DoctorNote[]> {
    const notes = await db.doctor_notes.where("patient_id").equals(patientId).toArray()
    return notes.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  }

  // Cultures
  async insertCulture(culture: Omit<Culture, "id" | "created_at">) {
    const id = uuidv4()
    const newCulture: Culture = {
      ...culture,
      id,
      created_at: new Date().toISOString(),
    }

    await db.cultures.add(newCulture)
    await this.enqueueOperation("cultures", "insert", newCulture, id)
    return newCulture
  }

  async updateCulture(id: string, updates: Partial<Culture>) {
    await db.cultures.update(id, updates)
    await this.enqueueOperation("cultures", "update", { id, ...updates })
  }

  async deleteCulture(id: string) {
    await db.cultures.delete(id)
    await this.enqueueOperation("cultures", "delete", { id })
  }

  async getCultures(patientId: string): Promise<Culture[]> {
    const cultures = await db.cultures.where("patient_id").equals(patientId).toArray()
    
    // No need to join - status is now a direct string value
    return cultures.sort((a, b) => new Date(b.requested_date).getTime() - new Date(a.requested_date).getTime())
  }

  // Radiology
  async insertRadiology(radiology: Omit<Radiology, "id" | "created_at">) {
    const id = uuidv4()
    const newRadiology: Radiology = {
      ...radiology,
      id,
      created_at: new Date().toISOString(),
    }

    await db.radiology.add(newRadiology)
    await this.enqueueOperation("radiology", "insert", newRadiology, id)
    return newRadiology
  }

  async updateRadiology(id: string, updates: Partial<Radiology>) {
    await db.radiology.update(id, updates)
    await this.enqueueOperation("radiology", "update", { id, ...updates })
  }

  async deleteRadiology(id: string) {
    await db.radiology.delete(id)
    await this.enqueueOperation("radiology", "delete", { id })
  }

  async getRadiology(patientId: string): Promise<Radiology[]> {
    const radiology = await db.radiology.where("patient_id").equals(patientId).toArray()
    
    // No need to join - scan_type and status are now direct string values
    return radiology.sort((a, b) => new Date(b.scan_date).getTime() - new Date(a.scan_date).getTime())
  }

  // Outbox operations
  async getOutboxCount(): Promise<number> {
    return await db.outbox.count()
  }

  async getOutboxOperations() {
    return await db.outbox.orderBy("created_at").toArray()
  }

  async deleteOutboxOperation(id: string) {
    await db.outbox.delete(id)
  }

  async updateOutboxOperation(id: string, updates: any) {
    await db.outbox.update(id, updates)
  }
}

export const dataClient = new DataClient()
