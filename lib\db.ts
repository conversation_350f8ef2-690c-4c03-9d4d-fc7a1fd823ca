"use client"

import <PERSON><PERSON>, { type Table } from "dexie"
import type {
  Patient,
  VitalSigns,
  LabValues,
  Medication,
  MedicationName,
  DoctorNote,
  Culture,
  Radiology,
  OutboxOperation,
  IdMapping,
  UnitType,
} from "./types"

export class ICUDatabase extends Dexie {
  // Reference tables (only unit_types remains)
  unit_types!: Table<UnitType>
  
  // Main tables
  patients!: Table<Patient>
  vital_signs!: Table<VitalSigns>
  lab_values!: Table<LabValues>
  medications!: Table<Medication>
  medication_names!: Table<MedicationName>
  medication_adherence!: Table<import("./types").MedicationAdherence>
  doctor_notes!: Table<DoctorNote>
  cultures!: Table<Culture>
  radiology!: Table<Radiology>
  outbox!: Table<OutboxOperation>
  id_mappings!: Table<IdMapping>

  constructor() {
    super("ICUDatabase")
    // Base schema
    this.version(1).stores({
      patients: "id, patient_id, name, admission_date, created_at",
      vital_signs: "id, patient_id, date, created_at, [patient_id+date]",
      lab_values: "id, patient_id, date, created_at, [patient_id+date]",
      medications: "id, patient_id, medication_name, date_prescribed, is_active",
      medication_names: "id, name",
      doctor_notes: "id, patient_id, date, created_at",
      cultures: "id, patient_id, requested_date, status, microorganism",
      radiology: "id, patient_id, scan_date, scan_type, status",
      outbox: "id, table_name, operation, created_at, retries",
      id_mappings: "local_id, server_id, table_name",
    })

    // Add medication adherence in a new version to handle upgrades
    this.version(2).stores({
      medication_adherence:
        "id, patient_id, medication_id, date, is_taking_medication, [patient_id+medication_id+date]",
    })

    // Add vital signs JSONB structure in version 3
    this.version(3).stores({
      vital_signs: "id, patient_id, date, vital_signs_data, created_at, [patient_id+date]",
    })

    // Version 6: Remove lookup tables, use direct string values
    this.version(6).stores({
      // Only unit_types remains as reference table
      unit_types: "id, name",
      
      // Update main tables to use direct string values
      patients: "id, patient_id, name, gender, unit_id, admission_date, created_at",
      cultures: "id, patient_id, requested_date, status, microorganism",
      radiology: "id, patient_id, scan_date, scan_type, status",
    }).upgrade(async (tx) => {
      // Seed unit_types from online database on first load
      try {
        // Import supabase dynamically to avoid circular dependencies
        const { supabase } = await import('./supabase')
        
        const { data: onlineUnits, error } = await supabase
          .from('unit_types')
          .select('id, name')
          .order('name')

        if (!error && onlineUnits && onlineUnits.length > 0) {
          // Transform online data to match UnitType interface
          const now = new Date().toISOString()
          const transformedUnits = onlineUnits.map(unit => ({
            ...unit,
            is_active: true,
            created_at: now
          }))
          await tx.table('unit_types').bulkAdd(transformedUnits)
        } else {
          // Fallback to hardcoded units if online fetch fails
          console.log('Online fetch failed or returned no data, using fallback units')
          const now = new Date().toISOString()
          await tx.table('unit_types').bulkAdd([
            { id: 1, name: 'ICU A', is_active: true, created_at: now },
            { id: 2, name: 'ICU B', is_active: true, created_at: now }
          ])
        }
      } catch (error) {
        console.error('Error fetching units from online DB during upgrade:', error)
        // Fallback to hardcoded units
        const now = new Date().toISOString()
        await tx.table('unit_types').bulkAdd([
          { id: 1, name: 'ICU A', is_active: true, created_at: now },
          { id: 2, name: 'ICU B', is_active: true, created_at: now }
        ])
      }
    })
  }

  // Helper methods for reference data (only unit_types remains)
  async getUnitTypes() {
    return await this.unit_types.toArray()
  }
}

export const db = new ICUDatabase()
