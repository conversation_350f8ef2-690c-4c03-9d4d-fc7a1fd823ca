import type { UserRole, GenderType, CultureStatus, RadiologyScanType, RadiologyStatus } from './constants'

export interface User {
  id: string
  username: string
  role: User<PERSON><PERSON>
}

export interface Patient {
  id: string
  patient_id: string
  name: string
  gender: GenderType
  age: number
  weight: number
  admission_date: string
  unit_id?: number
  unit?: UnitType // Optional joined data (unit_types table still exists)
  main_complaint?: string
  medical_history?: string
  initial_diagnosis?: string
  is_active?: boolean
  is_discharged?: boolean
  is_deceased?: boolean
  diseases: string[]
  created_at: string
  updated_at: string
}

export interface UnitType {
  id: number
  name: string
  description?: string
  is_active: boolean
  created_at: string
}

export interface VitalSigns {
  id: string
  patient_id: string
  date: string
  vital_signs_data: {
    heart_rate?: number
    systolic_bp?: number
    diastolic_bp?: number
    temperature?: number
    respiratory_rate?: number
    oxygen_saturation?: number
    cvp?: number
    fluid_balance?: number
    fluid_intake?: number
    fluid_output?: number
    sofa_score?: number
    apache_score?: number
    rifle_score?: number
    on_ventilator: boolean
    on_support: boolean
    on_dialysis: boolean
  }
  created_at: string
}

export interface LabValues {
  id: string
  patient_id: string
  date: string
  lab_data: Record<string, any>
  created_at: string
}

export interface Medication {
  id: string
  patient_id: string
  medication_name: string
  dosage?: string
  frequency?: string
  date_prescribed: string
  is_active: boolean
  created_at: string
}

export interface MedicationName {
  id: string
  name: string
  created_at: string
}

export interface MedicationAdherence {
  id: string
  patient_id: string
  medication_id: string
  date: string
  is_taking_medication: boolean
  created_at: string
}

export interface DoctorNote {
  id: string
  patient_id: string
  date: string
  content: string
  doctor_name?: string
  created_at: string
}

export interface Culture {
  id: string
  patient_id: string
  requested_date: string
  result_date?: string
  culture_type?: string
  results?: string
  microorganism?: string
  status: CultureStatus
  created_at: string
}

export interface Radiology {
  id: string
  patient_id: string
  scan_type: RadiologyScanType
  scan_date: string
  body_part?: string
  findings?: string
  status: RadiologyStatus
  created_at: string
}

export interface OutboxOperation {
  id: string
  table_name: string
  operation: "insert" | "update" | "delete"
  data: any
  local_id?: string
  server_id?: string
  created_at: string
  retries: number
  error?: string
}

export interface IdMapping {
  local_id: string
  server_id: string
  table_name: string
}
