"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { ArrowLeft, Heart, TestTube, Pill, Microscope, Scan, FileText } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { ProtectedLayout } from "@/components/protected-layout"
import { VitalsSection } from "@/components/sections/vitals-section"
import { LabsSection } from "@/components/sections/labs-section"
import { MedicationsSection } from "@/components/sections/medications-section"
import { CulturesSection } from "@/components/sections/cultures-section"
import { RadiologySection } from "@/components/sections/radiology-section"
import { NotesSection } from "@/components/sections/notes-section"
import { dataClient } from "@/lib/data-client"
import type { Patient } from "@/lib/types"

interface PatientDetailPageProps {
  params: { id: string }
}

export default function PatientDetailPage({ params }: PatientDetailPageProps) {
  const [patient, setPatient] = useState<Patient | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    loadPatient()
  }, [params.id])

  const loadPatient = async () => {
    try {
      const patientData = await dataClient.getPatient(params.id)
      setPatient(patientData || null)
    } catch (error) {
      console.error("Error loading patient:", error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">Loading patient data...</div>
        </div>
      </ProtectedLayout>
    )
  }

  if (!patient) {
    return (
      <ProtectedLayout>
        <div className="text-center py-12">
          <p className="text-muted-foreground">Patient not found.</p>
          <Button onClick={() => router.push("/")} className="mt-4">
            Back to Dashboard
          </Button>
        </div>
      </ProtectedLayout>
    )
  }

  return (
    <ProtectedLayout>
      <div className="space-y-6">
        <Button variant="ghost" onClick={() => router.push("/")}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>

        {/* Patient Header */}
        <Card>
          <CardContent className="p-3 bg-[#f1f5f9]">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="col-span-full border-b border-gray-400 pb-2 flex flex-col gap-2 md:grid md:grid-cols-3 md:gap-6">
                <div className="flex flex-row justify-between w-full md:flex-col md:gap-0">
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-muted-foreground">Patient Name:</h3>
                    <p className="text-lg font-bold">{patient.name}</p>
                  </div>
                  <div className="flex-1 flex justify-end md:hidden">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Admission Date:</h3>
                      <p className="text-sm font-semibold">
                        {new Date(patient.admission_date).toLocaleDateString("en-US", {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="mt-2 md:mt-0">
                  <h3 className="text-sm font-medium text-muted-foreground">Patient ID:</h3>
                  <p className="text-sm font-semibold">{patient.patient_id}</p>
                </div>
                <div className="mt-2 md:mt-0 hidden md:block">
                  <h3 className="text-sm font-medium text-muted-foreground">Admission Date:</h3>
                  <p className="text-sm font-semibold">
                    {new Date(patient.admission_date).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </p>
                </div>
              </div>
              <div className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Gender</h3>
                    <Badge variant="outline">{patient.gender}</Badge>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Age</h3>
                    <p className="font-semibold">{patient.age} years</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Weight</h3>
                    <p className="font-semibold">{patient.weight} kg</p>
                  </div>
                </div>
              </div>
            </div>

            {patient.diseases.length > 0 && (
              <div className="mt-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Chronic Diseases</h3>
                <div className="flex flex-wrap gap-2">
                  {patient.diseases.map((disease) => (
                    <Badge key={disease} variant="secondary">
                      {disease}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Medical History</h3>
                <p className="mt-1">{patient.medical_history || "Not specified"}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Main Complaint</h3>
                <p className="mt-1">{patient.main_complaint || "Not specified"}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Initial Diagnosis</h3>
                <p className="mt-1">{patient.initial_diagnosis || "Not specified"}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Patient Data Tabs */}
        <Tabs defaultValue="vitals" className="space-y-4 ">
          <TabsList className="grid w-full grid-cols-6 bg-[#f1f5f9]">
            <TabsTrigger value="vitals" className="flex items-center gap-2">
              <Heart className="h-4 w-4" />
              <span className="hidden sm:inline">Vital Signs</span>
            </TabsTrigger>
            <TabsTrigger value="labs" className="flex items-center gap-2">
              <TestTube className="h-4 w-4" />
              <span className="hidden sm:inline">Lab Values</span>
            </TabsTrigger>
            <TabsTrigger value="medications" className="flex items-center gap-2">
              <Pill className="h-4 w-4" />
              <span className="hidden sm:inline">Medications</span>
            </TabsTrigger>
            <TabsTrigger value="cultures" className="flex items-center gap-2">
              <Microscope className="h-4 w-4" />
              <span className="hidden sm:inline">Cultures</span>
            </TabsTrigger>
            <TabsTrigger value="radiology" className="flex items-center gap-2">
              <Scan className="h-4 w-4" />
              <span className="hidden sm:inline">Radiology</span>
            </TabsTrigger>
            <TabsTrigger value="notes" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              <span className="hidden sm:inline">Doctor Notes</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="vitals">
            <VitalsSection patientId={patient.patient_id} />
          </TabsContent>

          <TabsContent value="labs">
            <LabsSection patientId={patient.patient_id} />
          </TabsContent>

          <TabsContent value="medications">
            <MedicationsSection patientId={patient.patient_id} />
          </TabsContent>

          <TabsContent value="cultures">
            <CulturesSection patientId={patient.patient_id} />
          </TabsContent>

          <TabsContent value="radiology">
            <RadiologySection patientId={patient.patient_id} />
          </TabsContent>

          <TabsContent value="notes">
            <NotesSection patientId={patient.patient_id} />
          </TabsContent>
        </Tabs>
      </div>
    </ProtectedLayout>
  )
}
